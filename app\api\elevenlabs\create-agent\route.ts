import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/authOptions';
import { ElevenLabsClient } from 'elevenlabs';
import { checkUserAgentExists, checkPMOAgentExists, validateAndFixUserAgentConfig, applyAgentConfigurationFix, findAgentByExactName, searchAgentsByName } from '../../../../lib/elevenlabs/agentValidation';
import { storeUserAgent, updateUserAgent } from '../../../../lib/firebase/userAgents';
import { uploadToKnowledgeBaseWithDeduplication } from '../../../../components/scriptreaderAI/uploadKnowledgebase';
import { updateAgentKnowledgeBase } from '../../../../components/scriptreaderAI/elevenlabs';

interface CreateAgentRequest {
  agentId: string;
  name: string;
  voiceId: string;
  prompt: string;
  knowledgeBase?: Array<{
    id: string;
    title: string;
    content: string;
    metadata?: Record<string, any>;
  }>;
}

/**
 * API endpoint for creating ElevenLabs conversational agents for PMO voice meetings
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.email;

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User email is required'
      }, { status: 400 });
    }

    // Parse request body
    const requestBody = await request.json();
    console.log(`[ELEVENLABS_AGENT] Received request body:`, JSON.stringify(requestBody, null, 2));

    const {
      agentId,
      name,
      voiceId,
      prompt,
      knowledgeBase = []
    }: CreateAgentRequest = requestBody;

    console.log(`[ELEVENLABS_AGENT] Parsed parameters:`, {
      agentId,
      name,
      voiceId,
      promptLength: prompt?.length || 0,
      knowledgeBaseCount: knowledgeBase?.length || 0
    });

    // Validate required parameters
    if (!agentId || !name || !voiceId || !prompt) {
      console.error(`[ELEVENLABS_AGENT] Missing required parameters:`, {
        hasAgentId: !!agentId,
        hasName: !!name,
        hasVoiceId: !!voiceId,
        hasPrompt: !!prompt
      });
      return NextResponse.json({
        error: 'Missing required parameters: agentId, name, voiceId, and prompt are required'
      }, { status: 400 });
    }

    // Get ElevenLabs API key
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'ElevenLabs API key not configured'
      }, { status: 500 });
    }

    console.log(`[ELEVENLABS_AGENT] Processing request for user: ${userId}`);

    // Step 1: Check if this specific PMO agent already exists
    console.log(`[ELEVENLABS_AGENT] Checking for existing PMO agent: ${agentId} for user: ${userId}`);
    const existenceCheck = await checkPMOAgentExists(userId, agentId, apiKey);

    if (existenceCheck.exists && existenceCheck.userAgent && existenceCheck.elevenLabsAgent) {
      console.log(`[ELEVENLABS_AGENT] Found existing valid agent: ${existenceCheck.userAgent.agentId}`);

      // Validate and potentially fix the agent configuration
      const configValidation = await validateAndFixUserAgentConfig(existenceCheck.userAgent.agentId, apiKey);

      if (configValidation.needsUpdate && configValidation.updatedConfig) {
        console.log(`[ELEVENLABS_AGENT] Applying configuration fixes to existing agent`);
        const fixResult = await applyAgentConfigurationFix(
          existenceCheck.userAgent.agentId,
          configValidation.updatedConfig,
          apiKey
        );

        if (!fixResult.success) {
          console.warn(`[ELEVENLABS_AGENT] Failed to apply configuration fix: ${fixResult.error}`);
        }
      }

      return NextResponse.json({
        success: true,
        agentId: existenceCheck.userAgent.agentId,
        conversationId: existenceCheck.userAgent.conversationId,
        name: existenceCheck.userAgent.agentName,
        voiceId: existenceCheck.userAgent.voiceId,
        documentsUploaded: existenceCheck.userAgent.metadata?.documentsUploaded || 0,
        message: 'Existing agent found and validated',
        wasExisting: true,
        configurationIssues: configValidation.issues,
        configurationFixed: configValidation.needsUpdate && configValidation.updatedConfig ? true : false
      });
    }

    console.log(`[ELEVENLABS_AGENT] No existing agent found in database for user-generated ID: ${agentId}. Creating new agent...`);

    // Generate the proper PMO agent name for the new agent
    const { generatePMOAgentName, getPMOAgentConfig } = await import('../../../../lib/agents/voice/pmoAgentVoiceConfig');
    const properAgentName = generatePMOAgentName(userId, name);
    const agentConfig = getPMOAgentConfig(name);
    const displayName = agentConfig?.agentName || name;
    console.log(`[ELEVENLABS_AGENT] Will create new agent with name: ${properAgentName} (display: ${displayName})`);

    console.log(`[ELEVENLABS_AGENT] Creating new agent: ${agentId} (${properAgentName})`);

    // Step 1.5: Final check - ensure no agent with this name exists in ElevenLabs
    const { verifyAgentInElevenLabsByName } = await import('../../../../lib/elevenlabs/agentValidation');
    const finalCheck = await verifyAgentInElevenLabsByName(properAgentName, apiKey);

    if (finalCheck.exists) {
      console.log(`[ELEVENLABS_AGENT] Agent with name "${properAgentName}" already exists in ElevenLabs: ${finalCheck.agentData?.agent_id}`);
      return NextResponse.json({
        success: true,
        agentId: finalCheck.agentData.agent_id,
        conversationId: null,
        name: properAgentName,
        voiceId,
        documentsUploaded: 0,
        message: 'Existing agent found in ElevenLabs',
        wasExisting: true,
        configurationIssues: [],
        configurationFixed: false
      });
    }

    // Step 2: Create the conversational agent using ElevenLabs SDK
    let agentData: any = null; // Declare agentData outside try block

    try {
      console.log(`[ELEVENLABS_AGENT] Initializing ElevenLabs client...`);
      const client = new ElevenLabsClient({ apiKey });

      console.log(`[ELEVENLABS_AGENT] Building agent configuration...`);
      const agentConfig = {
        name: properAgentName, // Use the proper PMO agent name
        // Note: agent_id removed as it's not used in working userAgentManager
        conversation_config: {
          agent: {
            prompt: {
              prompt: prompt,
            },
            // Use Gemini 2.5 Flash for optimal performance (as per working example)
            llm: {
              model: "gpt-4o-mini"
            },
            language: 'en',
            first_message: `Hello! I'm ${displayName}, ready to assist you with your project management needs.`
          },
          tts: {
            voice_id: voiceId,
            // Note: model_id removed to use ElevenLabs default (following working userAgentManager pattern)
            stability: 0.5,
            speed: 1.0,
            similarity_boost: 0.8,
            optimize_streaming_latency: 4 // Maximum responsiveness
          },
          conversation: {
            max_duration_seconds: 1800, // 30 minutes
            text_only: false,
            // CRITICAL: Enable audio and text response transmission over WebSocket
            client_events: [
              "conversation_initiation_metadata" as const,
              "agent_response" as const,
              "user_transcript" as const,
              "audio" as const // CRITICAL: Enable audio streaming to client
            ]
          },
          turn: {
            turn_timeout: 10,
            silence_end_call_timeout: 60,
            mode: 'silence' as const
          },
          asr: {
            quality: 'high' as const,
            provider: 'elevenlabs' as const
          }
        }
      };

      console.log(`[ELEVENLABS_AGENT] Creating agent with SDK:`, {
        name: agentConfig.name,
        agent_id: agentId,
        voice_id: voiceId,
        prompt_length: prompt.length
      });

      // Log the full agent configuration being sent
      console.log(`[ELEVENLABS_AGENT] Full agent configuration:`, JSON.stringify(agentConfig, null, 2));

      console.log(`[ELEVENLABS_AGENT] Calling client.conversationalAi.createAgent...`);
      const agentResponse = await client.conversationalAi.createAgent(agentConfig);

      // Log the full response from ElevenLabs
      console.log(`[ELEVENLABS_AGENT] Raw agent creation response:`, JSON.stringify(agentResponse, null, 2));
      console.log(`[ELEVENLABS_AGENT] Agent response type:`, typeof agentResponse);
      console.log(`[ELEVENLABS_AGENT] Agent response keys:`, Object.keys(agentResponse || {}));

      if (!agentResponse) {
        console.error(`[ELEVENLABS_AGENT] No response received from ElevenLabs`);
        throw new Error('Agent creation failed - no response from ElevenLabs');
      }

      if (!agentResponse.agent_id) {
        console.error(`[ELEVENLABS_AGENT] No agent_id in response:`, agentResponse);
        throw new Error('Agent creation failed - no agent ID returned');
      }

      console.log(`[ELEVENLABS_AGENT] Agent created successfully: ${agentResponse.agent_id}`);

      agentData = {
        agent_id: agentResponse.agent_id,
        name: agentConfig.name,
        created_at: new Date().toISOString()
      };

      console.log(`[ELEVENLABS_AGENT] Created agentData object:`, JSON.stringify(agentData, null, 2));

      // Step 2.5: Validate and fix agent configuration
      console.log(`[ELEVENLABS_AGENT] Validating agent configuration for ${agentData.agent_id}`);
      const configValidation = await validateAndFixUserAgentConfig(agentData.agent_id, apiKey);

      if (configValidation.needsUpdate && configValidation.updatedConfig) {
        console.log(`[ELEVENLABS_AGENT] Applying configuration fixes to new agent`);
        const fixResult = await applyAgentConfigurationFix(
          agentData.agent_id,
          configValidation.updatedConfig,
          apiKey
        );

        if (!fixResult.success) {
          console.warn(`[ELEVENLABS_AGENT] Failed to apply configuration fix: ${fixResult.error}`);
        } else {
          console.log(`[ELEVENLABS_AGENT] Agent configuration fixed successfully`);
        }
      } else {
        console.log('[ELEVENLABS_AGENT] Agent configuration is valid');
      }

      console.log(`[ELEVENLABS_AGENT] Exiting agent creation try block. agentData:`, agentData);

    } catch (sdkError: any) {
      console.error('[ELEVENLABS_AGENT] SDK agent creation failed:', {
        error: sdkError.message,
        stack: sdkError.stack,
        errorName: sdkError.name,
        status: sdkError.status,
        response: sdkError.response,
        agentId,
        agentName: name
      });

      // Provide more specific error information
      let errorMessage = `Failed to create ElevenLabs agent: ${sdkError.message}`;
      if (sdkError.status) {
        errorMessage += ` (Status: ${sdkError.status})`;
      }

      return NextResponse.json({
        error: errorMessage,
        details: sdkError.stack,
        status: sdkError.status
      }, { status: sdkError.status || 500 });
    }

    // Check if agent was created successfully
    console.log(`[ELEVENLABS_AGENT] Checking agentData after try-catch. agentData:`, agentData);
    if (!agentData) {
      console.error(`[ELEVENLABS_AGENT] agentData is null/undefined - agent creation must have failed`);
      return NextResponse.json({
        error: 'Agent creation failed - no agent data available'
      }, { status: 500 });
    }

    console.log(`[ELEVENLABS_AGENT] agentData validation passed. Proceeding with knowledge base setup.`);

    // Step 3: Store agent in database
    let userAgentId: string | null = null;
    try {
      console.log(`[ELEVENLABS_AGENT] Storing agent in database for user: ${userId}`);
      userAgentId = await storeUserAgent({
        userId,
        agentId: agentData.agent_id,
        agentName: agentId, // Store the user-generated ID as the agent name for lookup
        voiceId,
        metadata: {
          prompt,
          documentsUploaded: knowledgeBase.length,
          actualAgentName: properAgentName // Store the actual ElevenLabs agent name in metadata
        }
      });
      console.log(`[ELEVENLABS_AGENT] Agent stored in database with ID: ${userAgentId}`);

      // Verify the agent can be found in database to prevent race conditions
      const { getUserAgentByUserGeneratedId } = await import('../../../../lib/firebase/userAgents');
      const verifyAgent = await getUserAgentByUserGeneratedId(userId, agentId);
      if (verifyAgent) {
        console.log(`[ELEVENLABS_AGENT] Agent successfully verified in database: ${verifyAgent.agentId}`);
      } else {
        console.warn(`[ELEVENLABS_AGENT] Warning: Agent not immediately findable in database after storage`);
      }
    } catch (dbError) {
      console.error('[ELEVENLABS_AGENT] Error storing agent in database:', dbError);
      // Continue anyway - agent was created successfully in ElevenLabs
    }

    // Step 4: Upload knowledge base documents if provided using proper deduplication
    let documentsUploaded = 0;
    if (knowledgeBase.length > 0) {
      try {
        console.log(`[ELEVENLABS_AGENT] Processing ${knowledgeBase.length} documents for knowledge base using deduplication`);

        // Process each document using the proper upload functions
        for (const doc of knowledgeBase) {
          try {
            console.log(`[ELEVENLABS_AGENT] Uploading document: ${doc.title}`);

            // Create a temporary file URL for the document content
            // Since we have text content, we'll create a blob URL
            const blob = new Blob([doc.content], { type: 'text/plain' });
            const tempFileUrl = URL.createObjectURL(blob);

            // Upload document with deduplication
            const uploadResult = await uploadToKnowledgeBaseWithDeduplication(
              tempFileUrl,
              doc.title,
              'text/plain',
              apiKey,
              false // Don't force upload, use deduplication
            );

            console.log(`[ELEVENLABS_AGENT] Upload result for ${doc.title}:`, uploadResult);

            // Associate document with agent (this will handle RAG indexing internally)
            console.log(`[ELEVENLABS_AGENT] Associating document with agent: ${agentData.agent_id}`);
            try {
              const agentUpdateResult = await updateAgentKnowledgeBase(
                agentData.agent_id,
                uploadResult.id,
                apiKey
              );
              console.log(`[ELEVENLABS_AGENT] Agent update result for ${doc.title}:`, agentUpdateResult);
            } catch (agentUpdateError) {
              console.error(`[ELEVENLABS_AGENT] Error updating agent for ${doc.title}:`, agentUpdateError);
              // Continue anyway - document was uploaded
            }

            // Clean up the temporary URL
            URL.revokeObjectURL(tempFileUrl);

            if (uploadResult.id) {
              documentsUploaded++;
              console.log(`[ELEVENLABS_AGENT] Successfully processed document: ${doc.title}`);
            }

          } catch (docError) {
            console.error(`[ELEVENLABS_AGENT] Error processing document ${doc.title}:`, docError);
            // Continue with other documents
          }
        }

        console.log(`[ELEVENLABS_AGENT] Completed document processing: ${documentsUploaded}/${knowledgeBase.length} documents uploaded`);

      } catch (kbError) {
        console.error('[ELEVENLABS_AGENT] Error setting up knowledge base:', kbError);
        // Continue without knowledge base - agent will still work
      }
    }

    // Step 5: Update database with document upload information
    if (userAgentId && documentsUploaded > 0) {
      try {
        await updateUserAgent(userAgentId, {
          metadata: {
            prompt,
            documentsUploaded,
            lastKnowledgeBaseUpdate: new Date()
          }
        });
        console.log(`[ELEVENLABS_AGENT] Updated database with document upload information: ${documentsUploaded} documents`);
      } catch (updateError) {
        console.error('[ELEVENLABS_AGENT] Error updating database with document info:', updateError);
      }
    }

    // Step 6: Create a conversation session using SDK
    let conversationId = null;
    // TODO: Fix conversation creation - the SDK method name needs to be determined
    console.log(`[ELEVENLABS_AGENT] Skipping conversation creation for now - will be handled by useConversation hook`);
    console.log(`[ELEVENLABS_AGENT] Agent created successfully and ready for voice conversations`);

    const finalResponse = {
      success: true,
      agentId: agentData.agent_id,
      conversationId,
      name: properAgentName, // Return the proper PMO agent name
      voiceId,
      documentsUploaded,
      message: `PMO agent created successfully with ${documentsUploaded} documents uploaded and ready for voice conversations`,
      wasExisting: false,
      userAgentId,
      configurationIssues: [],
      configurationFixed: false
    };

    console.log(`[ELEVENLABS_AGENT] Sending final success response:`, JSON.stringify(finalResponse, null, 2));
    return NextResponse.json(finalResponse);

  } catch (error: any) {
    console.error('[ELEVENLABS_AGENT] Error creating agent:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create ElevenLabs agent'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to check if an agent exists or list all agents
 * Query parameters:
 * - agentId: Check if specific agent exists
 * - list: Set to 'true' to list all agents
 * - search: Search agents by name pattern
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const listAgents = searchParams.get('list') === 'true';
    const searchQuery = searchParams.get('search');

    // Get ElevenLabs API key
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'ElevenLabs API key not configured'
      }, { status: 500 });
    }

    const client = new ElevenLabsClient({ apiKey });

    // Handle agent listing
    if (listAgents) {
      try {
        console.log(`[ELEVENLABS_AGENT] Listing all agents${searchQuery ? ` with search: ${searchQuery}` : ''}`);

        // Use direct API call since SDK method structure is unclear
        const url = new URL('https://api.elevenlabs.io/v1/convai/agents');
        if (searchQuery) {
          url.searchParams.append('search', searchQuery);
        }
        url.searchParams.append('page_size', '100');

        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'xi-api-key': apiKey,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to list agents: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const agentsResponse = await response.json();
        console.log(`[ELEVENLABS_AGENT] Found ${agentsResponse.agents?.length || 0} agents`);

        return NextResponse.json({
          success: true,
          agents: agentsResponse.agents || [],
          has_more: agentsResponse.has_more || false,
          next_cursor: agentsResponse.next_cursor || null
        });
      } catch (listError: any) {
        console.error('[ELEVENLABS_AGENT] Error listing agents:', listError);
        return NextResponse.json({
          error: 'Failed to list agents',
          details: listError.message
        }, { status: 500 });
      }
    }

    // Handle single agent existence check
    if (!agentId) {
      return NextResponse.json({
        error: 'agentId parameter is required when not listing agents'
      }, { status: 400 });
    }

    // Check if agent exists using SDK
    try {
      const agentData = await client.conversationalAi.getAgent(agentId);

      if (agentData && agentData.agent_id === agentId) {
        return NextResponse.json({
          exists: true,
          agent: agentData
        });
      } else {
        return NextResponse.json({
          exists: false
        });
      }
    } catch (fetchError: any) {
      // 404 means agent doesn't exist - this is expected for new agents
      if (fetchError.message && (fetchError.message.includes('404') || fetchError.message.includes('not found'))) {
        return NextResponse.json({
          exists: false
        });
      } else {
        return NextResponse.json({
          error: 'Failed to check agent status',
          details: fetchError.message
        }, { status: 500 });
      }
    }

  } catch (error: any) {
    console.error('[ELEVENLABS_AGENT] Error in GET endpoint:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to process request'
    }, { status: 500 });
  }
}
