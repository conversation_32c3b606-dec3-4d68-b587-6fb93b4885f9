/**
 * ElevenLabs Agent Validation and Existence Checking
 * 
 * This module provides utilities for validating agent existence and configuration
 * to prevent duplicate agents and ensure proper setup.
 */

import { ElevenLabsClient } from 'elevenlabs';
import { getUserAgent, getUserAgentByAgentId, getUserAgentByUserGeneratedId, updateUserAgent, UserAgent } from '../firebase/userAgents';

export interface AgentExistenceResult {
  exists: boolean;
  userAgent?: UserAgent;
  elevenLabsAgent?: any;
  needsCreation: boolean;
  error?: string;
}

export interface AgentValidationResult {
  isValid: boolean;
  needsUpdate: boolean;
  issues: string[];
  updatedConfig?: any;
}

/**
 * Check if a user already has a valid agent
 * This implements the complete existence checking flow:
 * 1. Check database for existing agent ID
 * 2. Verify agent still exists in ElevenLabs
 * 3. Return comprehensive status
 */
export async function checkUserAgentExists(
  userId: string,
  apiKey?: string
): Promise<AgentExistenceResult> {
  try {
    console.log(`[AGENT_VALIDATION] Checking agent existence for user: ${userId}`);

    // Step 1: Check if user has an agent stored in our database
    const userAgent = await getUserAgent(userId);

    if (!userAgent) {
      console.log(`[AGENT_VALIDATION] No agent found in database for user: ${userId}`);
      return {
        exists: false,
        needsCreation: true
      };
    }

    console.log(`[AGENT_VALIDATION] Found stored agent: ${userAgent.agentId} for user: ${userId}`);

    // Step 2: Verify the agent still exists in ElevenLabs
    const elevenLabsValidation = await verifyAgentInElevenLabs(userAgent.agentId, apiKey);

    if (!elevenLabsValidation.exists) {
      console.log(`[AGENT_VALIDATION] Agent ${userAgent.agentId} no longer exists in ElevenLabs`);

      // Mark the stored agent as inactive since it doesn't exist in ElevenLabs
      await updateUserAgent(userAgent.id, {
        status: 'error',
        lastVerified: new Date()
      });

      return {
        exists: false,
        userAgent,
        needsCreation: true,
        error: 'Agent no longer exists in ElevenLabs'
      };
    }

    // Step 3: Agent exists in both database and ElevenLabs
    console.log(`[AGENT_VALIDATION] Agent ${userAgent.agentId} verified in ElevenLabs`);

    // Update last verified timestamp
    await updateUserAgent(userAgent.id, {
      lastVerified: new Date()
    });

    return {
      exists: true,
      userAgent,
      elevenLabsAgent: elevenLabsValidation.agentData,
      needsCreation: false
    };

  } catch (error) {
    console.error('[AGENT_VALIDATION] Error checking user agent existence:', error);
    return {
      exists: false,
      needsCreation: true,
      error: error instanceof Error ? error.message : 'Unknown error during validation'
    };
  }
}

/**
 * Check if a specific PMO agent already exists for a user
 * This is specifically designed for PMO agents with specific agent IDs
 */
export async function checkPMOAgentExists(
  userId: string,
  agentId: string,
  apiKey?: string
): Promise<AgentExistenceResult> {
  try {
    console.log(`[AGENT_VALIDATION] Checking PMO agent existence for user: ${userId}, agentId: ${agentId}`);

    // Step 1: Check if this is a user-generated ID (contains email and pmo pattern)
    let userAgent: UserAgent | null = null;

    if (agentId.includes('@') && agentId.includes('-pmo-')) {
      // This looks like a user-generated ID, try to find by user-generated ID
      console.log(`[AGENT_VALIDATION] Detected user-generated ID pattern, searching by user-generated ID: ${agentId}`);
      userAgent = await getUserAgentByUserGeneratedId(userId, agentId);
    } else {
      // This might be an actual ElevenLabs agent ID
      console.log(`[AGENT_VALIDATION] Searching by ElevenLabs agent ID: ${agentId}`);
      userAgent = await getUserAgentByAgentId(agentId);
    }

    if (!userAgent) {
      console.log(`[AGENT_VALIDATION] No PMO agent found in database with identifier: ${agentId}`);

      // Also check if agent exists directly in ElevenLabs (in case database is out of sync)
      // Only do this if agentId doesn't look like a user-generated ID
      if (!agentId.includes('@') || !agentId.includes('-pmo-')) {
        const elevenLabsValidation = await verifyAgentInElevenLabs(agentId, apiKey);

        if (elevenLabsValidation.exists) {
          console.log(`[AGENT_VALIDATION] PMO agent ${agentId} exists in ElevenLabs but not in database`);
          return {
            exists: true,
            elevenLabsAgent: elevenLabsValidation.agentData,
            needsCreation: false,
            error: 'Agent exists in ElevenLabs but not in database'
          };
        }
      }

      return {
        exists: false,
        needsCreation: true
      };
    }

    // Verify the user owns this agent
    if (userAgent.userId !== userId) {
      console.log(`[AGENT_VALIDATION] Agent ${agentId} belongs to different user: ${userAgent.userId}`);
      return {
        exists: false,
        needsCreation: true,
        error: 'Agent belongs to different user'
      };
    }

    console.log(`[AGENT_VALIDATION] Found stored PMO agent: ${userAgent.agentId} for user: ${userId}`);

    // Step 2: Verify the agent still exists in ElevenLabs
    const elevenLabsValidation = await verifyAgentInElevenLabs(userAgent.agentId, apiKey);

    if (!elevenLabsValidation.exists) {
      console.log(`[AGENT_VALIDATION] PMO agent ${userAgent.agentId} no longer exists in ElevenLabs`);

      // Mark the stored agent as inactive since it doesn't exist in ElevenLabs
      await updateUserAgent(userAgent.id, {
        status: 'error',
        lastVerified: new Date()
      });

      return {
        exists: false,
        userAgent,
        needsCreation: true,
        error: 'Agent no longer exists in ElevenLabs'
      };
    }

    // Step 3: Agent exists in both database and ElevenLabs
    console.log(`[AGENT_VALIDATION] PMO agent ${userAgent.agentId} verified in ElevenLabs`);

    // Update last verified timestamp
    await updateUserAgent(userAgent.id, {
      lastVerified: new Date()
    });

    return {
      exists: true,
      userAgent,
      elevenLabsAgent: elevenLabsValidation.agentData,
      needsCreation: false
    };

  } catch (error) {
    console.error('[AGENT_VALIDATION] Error checking PMO agent existence:', error);
    return {
      exists: false,
      needsCreation: true,
      error: error instanceof Error ? error.message : 'Unknown error during validation'
    };
  }
}

/**
 * Search for existing agents by name pattern in ElevenLabs
 */
export async function searchAgentsByName(
  namePattern: string,
  apiKey?: string
): Promise<{ agents: any[]; error?: string }> {
  try {
    const apiKeyToUse = apiKey || process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;

    if (!apiKeyToUse) {
      return {
        agents: [],
        error: 'ElevenLabs API key not configured'
      };
    }

    console.log(`[AGENT_VALIDATION] Searching for agents with pattern: ${namePattern}`);

    // Use direct API call since SDK method structure is unclear
    const url = new URL('https://api.elevenlabs.io/v1/convai/agents');
    url.searchParams.append('search', namePattern);
    url.searchParams.append('page_size', '100');

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'xi-api-key': apiKeyToUse,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to search agents: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const agentsResponse = await response.json();
    const agents = agentsResponse.agents || [];
    console.log(`[AGENT_VALIDATION] Found ${agents.length} agents matching pattern: ${namePattern}`);

    return {
      agents
    };
  } catch (error: any) {
    console.error(`[AGENT_VALIDATION] Error searching agents:`, error.message);
    return {
      agents: [],
      error: error.message || 'Unknown error'
    };
  }
}

/**
 * Find existing agent by exact name match
 */
export async function findAgentByExactName(
  agentName: string,
  apiKey?: string
): Promise<{ agent?: any; found: boolean; error?: string }> {
  try {
    const searchResult = await searchAgentsByName(agentName, apiKey);

    if (searchResult.error) {
      return {
        found: false,
        error: searchResult.error
      };
    }

    // Look for exact name match
    const exactMatch = searchResult.agents.find(agent =>
      agent.name === agentName
    );

    if (exactMatch) {
      console.log(`[AGENT_VALIDATION] Found exact match for agent name: ${agentName} (ID: ${exactMatch.agent_id})`);
      return {
        agent: exactMatch,
        found: true
      };
    }

    console.log(`[AGENT_VALIDATION] No exact match found for agent name: ${agentName}`);
    return {
      found: false
    };
  } catch (error: any) {
    console.error(`[AGENT_VALIDATION] Error finding agent by name:`, error.message);
    return {
      found: false,
      error: error.message || 'Unknown error'
    };
  }
}

/**
 * Verify that an agent exists in ElevenLabs
 */
export async function verifyAgentInElevenLabs(
  agentId: string, 
  apiKey?: string
): Promise<{ exists: boolean; agentData?: any; error?: string }> {
  try {
    const client = new ElevenLabsClient({ 
      apiKey: apiKey || process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY 
    });

    console.log(`[AGENT_VALIDATION] Verifying agent ${agentId} in ElevenLabs`);
    
    const agentData = await client.conversationalAi.getAgent(agentId);
    
    if (agentData && agentData.agent_id === agentId) {
      console.log(`[AGENT_VALIDATION] Agent ${agentId} verified successfully`);
      return {
        exists: true,
        agentData
      };
    } else {
      console.log(`[AGENT_VALIDATION] Agent ${agentId} verification failed - invalid response`);
      return {
        exists: false,
        error: 'Invalid agent data returned'
      };
    }
  } catch (error: any) {
    // 404 or "not found" errors mean the agent doesn't exist
    if (error.message && (
      error.message.includes('404') || 
      error.message.includes('not found') ||
      error.message.includes('Not Found')
    )) {
      console.log(`[AGENT_VALIDATION] Agent ${agentId} not found in ElevenLabs (404)`);
      return {
        exists: false,
        error: 'Agent not found'
      };
    }

    console.error(`[AGENT_VALIDATION] Error verifying agent ${agentId}:`, error);
    return {
      exists: false,
      error: error.message || 'Unknown error during verification'
    };
  }
}

/**
 * Validate and fix agent configuration to ensure text response transmission works
 * This is critical for proper WebSocket communication
 */
export async function validateAndFixUserAgentConfig(
  agentId: string, 
  apiKey?: string
): Promise<AgentValidationResult> {
  try {
    console.log(`[AGENT_VALIDATION] Validating configuration for agent: ${agentId}`);

    const client = new ElevenLabsClient({ 
      apiKey: apiKey || process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY 
    });

    // Get current agent configuration
    const agentData = await client.conversationalAi.getAgent(agentId);
    
    if (!agentData || !agentData.conversation_config) {
      return {
        isValid: false,
        needsUpdate: false,
        issues: ['Agent configuration not found']
      };
    }

    const issues: string[] = [];
    let needsUpdate = false;
    const config = agentData.conversation_config;

    // Check critical client_events for text response transmission
    const requiredEvents = [
      "conversation_initiation_metadata" as const,
      "agent_response" as const,
      "user_transcript" as const,
      "audio" as const
    ];

    const currentEvents = config.conversation?.client_events || [];
    const missingEvents = requiredEvents.filter(event => !currentEvents.includes(event));

    if (missingEvents.length > 0) {
      issues.push(`Missing client events: ${missingEvents.join(', ')}`);
      needsUpdate = true;
    }

    // Check if text_only is properly configured (check if it exists in the config)
    if ((config.conversation as any)?.text_only === true) {
      issues.push('Agent configured for text-only mode, audio streaming disabled');
      needsUpdate = true;
    }

    // Check TTS configuration
    if (!config.tts?.voice_id) {
      issues.push('No voice ID configured for TTS');
      needsUpdate = true;
    }

    // Check ASR configuration
    if (!config.asr?.provider) {
      issues.push('No ASR provider configured');
      needsUpdate = true;
    }

    if (needsUpdate) {
      // Create updated configuration
      const updatedConfig = {
        ...config,
        conversation: {
          ...config.conversation,
          ...(config.conversation && 'text_only' in config.conversation ? { text_only: false } : {}),
          client_events: requiredEvents
        },
        asr: {
          ...config.asr,
          quality: 'high' as const,
          provider: 'elevenlabs' as const
        }
      };

      console.log(`[AGENT_VALIDATION] Agent ${agentId} needs configuration update`);
      return {
        isValid: false,
        needsUpdate: true,
        issues,
        updatedConfig
      };
    }

    console.log(`[AGENT_VALIDATION] Agent ${agentId} configuration is valid`);
    return {
      isValid: true,
      needsUpdate: false,
      issues: []
    };

  } catch (error) {
    console.error(`[AGENT_VALIDATION] Error validating agent configuration:`, error);
    return {
      isValid: false,
      needsUpdate: false,
      issues: [error instanceof Error ? error.message : 'Unknown validation error']
    };
  }
}

/**
 * Apply configuration fixes to an agent
 */
export async function applyAgentConfigurationFix(
  agentId: string,
  updatedConfig: any,
  apiKey?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`[AGENT_VALIDATION] Applying configuration fix to agent: ${agentId}`);

    const client = new ElevenLabsClient({ 
      apiKey: apiKey || process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY 
    });

    // Update the agent configuration
    await client.conversationalAi.updateAgent(agentId, {
      conversation_config: updatedConfig
    });

    console.log(`[AGENT_VALIDATION] Successfully updated agent ${agentId} configuration`);
    return { success: true };

  } catch (error) {
    console.error(`[AGENT_VALIDATION] Error applying configuration fix:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during configuration update'
    };
  }
}
