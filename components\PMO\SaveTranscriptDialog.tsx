"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '../ui/button';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '../ui/dialog';
import { 
  Download, 
  FileText, 
  Clock, 
  MessageSquare, 
  User, 
  Bot,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { TranscriptMessage } from './TranscriptPanel';
import { createTranscriptSummary, extractTranscriptMetadata } from '../../lib/utils/transcriptUtils';

interface SaveTranscriptDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (uploadToKnowledgeBase: boolean) => Promise<any>;
  dialogue: TranscriptMessage[];
  agentName: string;
  agentType: string;
  documentContext?: any;
  isLoading?: boolean;
}

export default function SaveTranscriptDialog({
  isOpen,
  onClose,
  onSave,
  dialogue,
  agentName,
  agentType,
  documentContext,
  isLoading = false
}: SaveTranscriptDialogProps) {
  const [uploadToKnowledgeBase, setUploadToKnowledgeBase] = useState(true);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Extract metadata for display
  const metadata = dialogue.length > 0 
    ? extractTranscriptMetadata(dialogue, agentName, agentType, documentContext)
    : null;

  const summary = metadata 
    ? createTranscriptSummary(dialogue, metadata)
    : 'No transcript available';

  const handleSave = async () => {
    setSaveStatus('saving');
    setErrorMessage('');
    
    try {
      await onSave(uploadToKnowledgeBase);
      setSaveStatus('success');
      
      // Auto-close after success
      setTimeout(() => {
        onClose();
        setSaveStatus('idle');
      }, 2000);
    } catch (error) {
      setSaveStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Failed to save transcript');
    }
  };

  const handleClose = () => {
    if (saveStatus !== 'saving') {
      onClose();
      setSaveStatus('idle');
      setErrorMessage('');
    }
  };

  if (dialogue.length === 0) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md bg-gray-800 border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center">
              <MessageSquare className="h-5 w-5 mr-2 text-blue-400" />
              No Transcript Available
            </DialogTitle>
            <DialogDescription className="text-gray-400">
              There is no conversation transcript to save. Start a conversation with the agent to generate a transcript.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="ghost" onClick={handleClose} className="text-gray-400 hover:text-white">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg bg-gray-800 border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-white flex items-center">
            <Download className="h-5 w-5 mr-2 text-blue-400" />
            Save Meeting Transcript
          </DialogTitle>
          <DialogDescription className="text-gray-400">
            Would you like to save the meeting transcript as a PDF?
          </DialogDescription>
        </DialogHeader>

        {/* Transcript Summary */}
        <div className="space-y-4">
          <div className="bg-gray-700/50 rounded-lg p-4 space-y-3">
            <h4 className="text-sm font-medium text-white flex items-center">
              <FileText className="h-4 w-4 mr-2 text-purple-400" />
              Transcript Summary
            </h4>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex items-center text-gray-300">
                  <Bot className="h-3 w-3 mr-2 text-green-400" />
                  <span>Agent: {agentName}</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <MessageSquare className="h-3 w-3 mr-2 text-blue-400" />
                  <span>Messages: {dialogue.length}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center text-gray-300">
                  <User className="h-3 w-3 mr-2 text-purple-400" />
                  <span>Your messages: {dialogue.filter(m => m.role.toLowerCase() === 'user').length}</span>
                </div>
                {metadata && (
                  <div className="flex items-center text-gray-300">
                    <Clock className="h-3 w-3 mr-2 text-yellow-400" />
                    <span>Duration: {Math.round((metadata.endTime.getTime() - metadata.startTime.getTime()) / 60000)} min</span>
                  </div>
                )}
              </div>
            </div>

            {(metadata?.documentTitle || metadata?.category) && (
              <div className="pt-2 border-t border-gray-600 space-y-1">
                {metadata.documentTitle && (
                  <p className="text-xs text-gray-400">
                    <strong>Document Context:</strong> {metadata.documentTitle}
                  </p>
                )}
                <p className="text-xs text-gray-400">
                  <strong>Storage Category:</strong> {metadata.category}
                </p>
              </div>
            )}
          </div>

          {/* Save Options */}
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="uploadToKnowledgeBase"
                checked={uploadToKnowledgeBase}
                onChange={(e) => setUploadToKnowledgeBase(e.target.checked)}
                disabled={saveStatus === 'saving'}
                className="mt-1 h-4 w-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
              />
              <div className="flex-1">
                <label htmlFor="uploadToKnowledgeBase" className="text-sm font-medium text-white cursor-pointer">
                  Upload to Agent Knowledge Base
                </label>
                <p className="text-xs text-gray-400 mt-1">
                  Add this transcript to the agent's knowledge base for future reference and improved responses.
                </p>
              </div>
            </div>
          </div>

          {/* Status Messages */}
          {saveStatus === 'saving' && (
            <div className="flex items-center space-x-2 text-blue-400 bg-blue-900/20 rounded-lg p-3">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Saving transcript...</span>
            </div>
          )}

          {saveStatus === 'success' && (
            <div className="flex items-center space-x-2 text-green-400 bg-green-900/20 rounded-lg p-3">
              <CheckCircle className="h-4 w-4" />
              <span className="text-sm">Transcript saved successfully!</span>
            </div>
          )}

          {saveStatus === 'error' && (
            <div className="flex items-center space-x-2 text-red-400 bg-red-900/20 rounded-lg p-3">
              <AlertCircle className="h-4 w-4" />
              <div className="flex-1">
                <span className="text-sm">Failed to save transcript</span>
                {errorMessage && (
                  <p className="text-xs text-red-300 mt-1">{errorMessage}</p>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="space-x-2">
          <Button 
            variant="ghost" 
            onClick={handleClose}
            disabled={saveStatus === 'saving'}
            className="text-gray-400 hover:text-white"
          >
            {saveStatus === 'success' ? 'Close' : 'Cancel'}
          </Button>
          
          {saveStatus !== 'success' && (
            <Button 
              onClick={handleSave}
              disabled={saveStatus === 'saving'}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {saveStatus === 'saving' ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Save Transcript
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
